import { ICertificateField } from '../models/certificate.model';

// Default certificate templates configuration
export const DEFAULT_TEMPLATES = [
	{
		name: 'Attendance Certificate',
		description: 'Standard certificate for event attendees',
		templateType: 'generated' as const,
		fields: [
			{
				id: 'title',
				name: 'Certificate Title',
				type: 'text' as const,
				x: 50,
				y: 20,
				fontSize: 32,
				fontFamily: 'Arial',
				color: '#1a365d',
				required: true,
			},
			{
				id: 'participantName',
				name: 'Participant Name',
				type: 'text' as const,
				x: 50,
				y: 40,
				fontSize: 24,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: true,
			},
			{
				id: 'eventName',
				name: 'Event Name',
				type: 'text' as const,
				x: 50,
				y: 55,
				fontSize: 18,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventDate',
				name: 'Event Date',
				type: 'date' as const,
				x: 50,
				y: 65,
				fontSize: 14,
				fontFamily: 'Arial',
				color: '#718096',
				required: true,
			},
			{
				id: 'organizerSignature',
				name: 'Organizer Signature',
				type: 'text' as const,
				x: 70,
				y: 85,
				fontSize: 12,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: false,
			},
		],
		defaultValues: {
			title: 'Certificate of Attendance',
		},
	},
	{
		name: 'Speaker Certificate',
		description: 'Certificate for event speakers and presenters',
		templateType: 'generated' as const,
		fields: [
			{
				id: 'title',
				name: 'Certificate Title',
				type: 'text' as const,
				x: 50,
				y: 20,
				fontSize: 32,
				fontFamily: 'Arial',
				color: '#744210',
				required: true,
			},
			{
				id: 'participantName',
				name: 'Speaker Name',
				type: 'text' as const,
				x: 50,
				y: 40,
				fontSize: 24,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: true,
			},
			{
				id: 'recognition',
				name: 'Recognition Text',
				type: 'text' as const,
				x: 50,
				y: 50,
				fontSize: 16,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventName',
				name: 'Event Name',
				type: 'text' as const,
				x: 50,
				y: 60,
				fontSize: 18,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventDate',
				name: 'Event Date',
				type: 'date' as const,
				x: 50,
				y: 70,
				fontSize: 14,
				fontFamily: 'Arial',
				color: '#718096',
				required: true,
			},
			{
				id: 'organizerSignature',
				name: 'Organizer Signature',
				type: 'text' as const,
				x: 70,
				y: 85,
				fontSize: 12,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: false,
			},
		],
		defaultValues: {
			title: 'Certificate of Recognition',
			recognition: 'for outstanding contribution as a speaker',
		},
	},
	{
		name: 'Volunteer Certificate',
		description: 'Certificate for event volunteers',
		templateType: 'generated' as const,
		fields: [
			{
				id: 'title',
				name: 'Certificate Title',
				type: 'text' as const,
				x: 50,
				y: 20,
				fontSize: 32,
				fontFamily: 'Arial',
				color: '#22543d',
				required: true,
			},
			{
				id: 'participantName',
				name: 'Volunteer Name',
				type: 'text' as const,
				x: 50,
				y: 40,
				fontSize: 24,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: true,
			},
			{
				id: 'appreciation',
				name: 'Appreciation Text',
				type: 'text' as const,
				x: 50,
				y: 50,
				fontSize: 16,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventName',
				name: 'Event Name',
				type: 'text' as const,
				x: 50,
				y: 60,
				fontSize: 18,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventDate',
				name: 'Event Date',
				type: 'date' as const,
				x: 50,
				y: 70,
				fontSize: 14,
				fontFamily: 'Arial',
				color: '#718096',
				required: true,
			},
			{
				id: 'organizerSignature',
				name: 'Organizer Signature',
				type: 'text' as const,
				x: 70,
				y: 85,
				fontSize: 12,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: false,
			},
		],
		defaultValues: {
			title: 'Certificate of Appreciation',
			appreciation: 'for dedicated volunteer service',
		},
	},
	{
		name: 'Participation Certificate',
		description: 'General participation certificate for all roles',
		templateType: 'generated' as const,
		fields: [
			{
				id: 'title',
				name: 'Certificate Title',
				type: 'text' as const,
				x: 50,
				y: 20,
				fontSize: 32,
				fontFamily: 'Arial',
				color: '#553c9a',
				required: true,
			},
			{
				id: 'participantName',
				name: 'Participant Name',
				type: 'text' as const,
				x: 50,
				y: 40,
				fontSize: 24,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: true,
			},
			{
				id: 'participantRole',
				name: 'Participant Role',
				type: 'text' as const,
				x: 50,
				y: 50,
				fontSize: 16,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventName',
				name: 'Event Name',
				type: 'text' as const,
				x: 50,
				y: 60,
				fontSize: 18,
				fontFamily: 'Arial',
				color: '#4a5568',
				required: true,
			},
			{
				id: 'eventDate',
				name: 'Event Date',
				type: 'date' as const,
				x: 50,
				y: 70,
				fontSize: 14,
				fontFamily: 'Arial',
				color: '#718096',
				required: true,
			},
			{
				id: 'organizerSignature',
				name: 'Organizer Signature',
				type: 'text' as const,
				x: 70,
				y: 85,
				fontSize: 12,
				fontFamily: 'Arial',
				color: '#2d3748',
				required: false,
			},
		],
		defaultValues: {
			title: 'Certificate of Participation',
		},
	},
];
